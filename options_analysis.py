import sys
import yfinance as yf
import pandas as pd
import numpy as np
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLineEdit, QLabel, QMessageBox,
                             QComboBox, QProgressBar)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QPalette, QColor
import pyqtgraph as pg

class DataFetcher(QThread):
    """Thread for fetching options data without blocking the GUI"""
    data_ready = pyqtSignal(pd.DataFrame, str)  # DataFrame and expiration date
    error_occurred = pyqtSignal(str)
    progress_update = pyqtSignal(str)

    def __init__(self, ticker):
        super().__init__()
        self.ticker = ticker

    def run(self):
        try:
            self.progress_update.emit("Fetching options data...")
            df, exp_date = self.fetch_options_data(self.ticker)
            self.data_ready.emit(df, exp_date)
        except Exception as e:
            self.error_occurred.emit(str(e))

    def fetch_options_data(self, ticker):
        """Fetch options data for the given ticker"""
        stock = yf.Ticker(ticker)

        # Check if options are available
        if not stock.options:
            raise ValueError(f"No options data available for {ticker}")

        exp_date = stock.options[0]  # nearest expiration
        self.progress_update.emit(f"Processing expiration: {exp_date}")

        chain = stock.option_chain(exp_date)
        calls = chain.calls.copy()
        puts = chain.puts.copy()

        # Handle NaN values in volume and lastPrice
        calls['volume'] = calls['volume'].fillna(0)
        puts['volume'] = puts['volume'].fillna(0)
        calls['lastPrice'] = calls['lastPrice'].fillna(0)
        puts['lastPrice'] = puts['lastPrice'].fillna(0)

        # Compute premium spent: volume * lastPrice * contract size (100)
        calls['premium_spent'] = calls['volume'] * calls['lastPrice'] * 100
        puts['premium_spent'] = puts['volume'] * puts['lastPrice'] * 100

        # Merge calls & puts on strike, fill missing with zero
        df = pd.merge(calls[['strike','premium_spent']],
                      puts[['strike','premium_spent']],
                      on='strike', how='outer',
                      suffixes=('_call','_put')).fillna(0)

        # Compute net premium (call minus put)
        df['net_premium'] = df['premium_spent_call'] - df['premium_spent_put']

        # Sort by strike
        df = df.sort_values('strike')

        return df, exp_date

class OptionsAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Options Net Premium Analyzer")
        self.setGeometry(100, 100, 1200, 800)

        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 12px;
            }
            QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #606060;
                padding: 5px;
                font-size: 12px;
            }
            QComboBox {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #606060;
                padding: 5px;
                font-size: 12px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #ffffff;
            }
            QPushButton {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #606060;
                padding: 8px 16px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #303030;
            }
            QPushButton:disabled {
                background-color: #303030;
                color: #808080;
            }
            QProgressBar {
                background-color: #404040;
                border: 1px solid #606060;
                text-align: center;
                color: #ffffff;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
            }
        """)

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)

        # Input section
        input_layout = QHBoxLayout()

        # Ticker input
        self.ticker_label = QLabel("Ticker Symbol:")
        self.ticker_input = QLineEdit("AAPL")
        self.ticker_input.setMaximumWidth(100)
        self.ticker_input.returnPressed.connect(self.fetch_data)

        # Popular tickers dropdown
        self.popular_label = QLabel("Popular Tickers:")
        self.popular_combo = QComboBox()
        self.popular_combo.addItems([
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA",
            "SPY", "QQQ", "IWM", "NFLX", "AMD", "INTC", "BABA"
        ])
        self.popular_combo.currentTextChanged.connect(self.on_ticker_selected)
        self.popular_combo.setMaximumWidth(100)

        # Fetch button
        self.fetch_button = QPushButton("Fetch Data")
        self.fetch_button.clicked.connect(self.fetch_data)

        input_layout.addWidget(self.ticker_label)
        input_layout.addWidget(self.ticker_input)
        input_layout.addWidget(self.popular_label)
        input_layout.addWidget(self.popular_combo)
        input_layout.addWidget(self.fetch_button)
        input_layout.addStretch()

        main_layout.addLayout(input_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Chart section
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setBackground('#2b2b2b')
        self.chart_widget.setLabel('left', 'Net Premium (Millions USD)', color='white', size='12pt')
        self.chart_widget.setLabel('bottom', 'Strike Price', color='white', size='12pt')
        self.chart_widget.getAxis('left').setPen(color='white')
        self.chart_widget.getAxis('bottom').setPen(color='white')
        self.chart_widget.getAxis('left').setTextPen(color='white')
        self.chart_widget.getAxis('bottom').setTextPen(color='white')
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)

        main_layout.addWidget(self.chart_widget)

        # Status label
        self.status_label = QLabel("Ready to fetch data...")
        main_layout.addWidget(self.status_label)

    def on_ticker_selected(self, ticker):
        """Update ticker input when popular ticker is selected"""
        self.ticker_input.setText(ticker)

    def fetch_data(self):
        """Start fetching data in a separate thread"""
        ticker = self.ticker_input.text().strip().upper()
        if not ticker:
            QMessageBox.warning(self, "Warning", "Please enter a ticker symbol")
            return

        self.fetch_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.status_label.setText(f"Fetching data for {ticker}...")

        # Start data fetching thread
        self.data_thread = DataFetcher(ticker)
        self.data_thread.data_ready.connect(self.update_chart)
        self.data_thread.error_occurred.connect(self.handle_error)
        self.data_thread.progress_update.connect(self.update_progress)
        self.data_thread.start()

    def update_progress(self, message):
        """Update progress message"""
        self.status_label.setText(message)

    def update_chart(self, df, exp_date):
        """Update the chart with new data"""
        self.fetch_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        if df.empty:
            self.status_label.setText("No data available for this ticker")
            return

        # Clear previous plot
        self.chart_widget.clear()

        # Prepare data for horizontal bar chart
        strikes = df['strike'].values
        net_premiums = df['net_premium'].values / 1e6  # Convert to millions

        # Create colors based on positive/negative values
        colors = ['green' if x > 0 else 'red' for x in net_premiums]

        # Create horizontal bar chart using BarGraphItem
        y_positions = np.arange(len(strikes))

        # Create individual bars with colors
        for i, (y_pos, premium, color) in enumerate(zip(y_positions, net_premiums, colors)):
            bar = pg.BarGraphItem(
                x=[0], y=[y_pos], width=[abs(premium)], height=0.8,
                brush=color, pen='white'
            )
            # Adjust x position for negative values
            if premium < 0:
                bar.setOpts(x=[premium])
            self.chart_widget.addItem(bar)

        # Add zero line
        zero_line = pg.InfiniteLine(pos=0, angle=90, pen=pg.mkPen('white', width=1))
        self.chart_widget.addItem(zero_line)

        # Set up y-axis with strike prices
        y_ticks = [(i, f"{strike:.0f}") for i, strike in enumerate(strikes)]
        self.chart_widget.getAxis('left').setTicks([y_ticks])

        # Invert y-axis so highest strikes are on top
        self.chart_widget.invertY(True)

        # Set title and labels
        ticker = self.ticker_input.text().strip().upper()
        self.chart_widget.setTitle(f'{ticker} Net Premium by Strike ({exp_date})', color='white', size='14pt')

        # Set axis ranges
        if len(net_premiums) > 0:
            x_range = max(abs(min(net_premiums)), abs(max(net_premiums))) * 1.1
            self.chart_widget.setXRange(-x_range, x_range)
            self.chart_widget.setYRange(-0.5, len(strikes) - 0.5)

        self.status_label.setText(f"Data loaded successfully for {ticker} ({len(df)} strikes)")

    def handle_error(self, error_message):
        """Handle errors from data fetching"""
        self.fetch_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("Error occurred while fetching data")
        QMessageBox.critical(self, "Error", f"Failed to fetch data:\n{error_message}")

def main():
    app = QApplication(sys.argv)

    # Set application style for dark theme
    app.setStyle('Fusion')

    window = OptionsAnalyzer()
    window.show()

    sys.exit(app.exec())

if __name__ == '__main__':
    main()
